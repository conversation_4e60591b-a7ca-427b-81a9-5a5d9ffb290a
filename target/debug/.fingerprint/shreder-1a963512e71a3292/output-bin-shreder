{"$message_type":"diagnostic","message":"unused imports: `ShredstreamClientConfig` and `ShredstreamSubscriptionFilters`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/common/client/mod.rs","byte_start":141,"byte_end":164,"line_start":8,"line_end":8,"column_start":18,"column_end":41,"is_primary":true,"text":[{"text":"pub use config::{ShredstreamClientConfig, ShredstreamSubscriptionFilters};","highlight_start":18,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/mod.rs","byte_start":166,"byte_end":196,"line_start":8,"line_end":8,"column_start":43,"column_end":73,"is_primary":true,"text":[{"text":"pub use config::{ShredstreamClientConfig, ShredstreamSubscriptionFilters};","highlight_start":43,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/common/client/mod.rs","byte_start":124,"byte_end":199,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use config::{ShredstreamClientConfig, ShredstreamSubscriptionFilters};","highlight_start":1,"highlight_end":75},{"text":"pub use shredstream_client::ShredstreamClient;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ShredstreamClientConfig` and `ShredstreamSubscriptionFilters`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/mod.rs:8:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use config::{ShredstreamClientConfig, ShredstreamSubscriptionFilters};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `shredstream_client::ShredstreamClient`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/common/client/mod.rs","byte_start":207,"byte_end":244,"line_start":9,"line_end":9,"column_start":9,"column_end":46,"is_primary":true,"text":[{"text":"pub use shredstream_client::ShredstreamClient;","highlight_start":9,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/common/client/mod.rs","byte_start":199,"byte_end":246,"line_start":9,"line_end":9,"column_start":1,"column_end":48,"is_primary":true,"text":[{"text":"pub use shredstream_client::ShredstreamClient;","highlight_start":1,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `shredstream_client::ShredstreamClient`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/mod.rs:9:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use shredstream_client::ShredstreamClient;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `shredstream_client::ShredstreamClient`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/main.rs","byte_start":178,"byte_end":215,"line_start":10,"line_end":10,"column_start":54,"column_end":91,"is_primary":true,"text":[{"text":"    client::{config::ShredstreamSubscriptionFilters, shredstream_client::ShredstreamClient},","highlight_start":54,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":176,"byte_end":215,"line_start":10,"line_end":10,"column_start":52,"column_end":91,"is_primary":true,"text":[{"text":"    client::{config::ShredstreamSubscriptionFilters, shredstream_client::ShredstreamClient},","highlight_start":52,"highlight_end":91}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/main.rs","byte_start":137,"byte_end":138,"line_start":10,"line_end":10,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"    client::{config::ShredstreamSubscriptionFilters, shredstream_client::ShredstreamClient},","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/main.rs","byte_start":215,"byte_end":216,"line_start":10,"line_end":10,"column_start":91,"column_end":92,"is_primary":true,"text":[{"text":"    client::{config::ShredstreamSubscriptionFilters, shredstream_client::ShredstreamClient},","highlight_start":91,"highlight_end":92}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `shredstream_client::ShredstreamClient`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:10:54\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    client::{config::ShredstreamSubscriptionFilters, shredstream_client::ShredstreamClient},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"3 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 3 warnings emitted\u001b[0m\n\n"}
